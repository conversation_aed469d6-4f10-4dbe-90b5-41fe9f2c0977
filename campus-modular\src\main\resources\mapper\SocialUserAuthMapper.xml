<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.business.mapper.SocialUserAuthMapper">

    <insert id="insertUserSocial">
        insert into `social_user` (`social_user_id`, `uuid`, `source`) value (#{socialUserId}, #{uuid}, #{source});

    </insert>

    <insert id="insertUserSocialAuth">
        insert into `social_user_auth` (`user_id`, `social_user_id`) value (#{userId}, #{socialUserId});

    </insert>


    <select id="getUserIdByWxamp" resultType="java.lang.Long">
        SELECT user_id
        FROM `social_user_auth` sua
                 INNER JOIN `social_user` su ON sua.social_user_id = su.social_user_id
        WHERE su.uuid = #{openid}
          AND su.source = 'WXAMP'

    </select>

    <select id="getSocialUserId" resultType="java.lang.Long">
        SELECT social_user_id
        FROM `social_user`
        WHERE uuid = #{uuid}
    </select>
</mapper>