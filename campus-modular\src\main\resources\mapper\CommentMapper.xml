<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.business.mapper.CommentMapper">

    <resultMap id="BaseResultMap" type="com.oddfar.campus.business.domain.entity.CommentEntity">
        <id property="commentId" column="comment_id" jdbcType="BIGINT"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="toUserId" column="user_id" jdbcType="BIGINT"/>
        <result property="oneLevelId" column="user_id" jdbcType="BIGINT"/>
        <result property="contentId" column="content_id" jdbcType="BIGINT"/>
        <result property="coContent" column="co_content" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        comment_id
        ,parent_id,user_id,toUserId,oneLevelId
        content_id,co_content,del_flag,
        create_time,create_user,update_time,
        update_user
    </sql>
    <select id="getOneLevel" parameterType="com.oddfar.campus.business.domain.entity.CommentEntity"
            resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id,co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               (select COUNT(*)  FROM campus_comment WHERE one_level_id = co.comment_id and del_flag = '0') children_count,
               u.nick_name as user_nick_name,u.avatar
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
        WHERE co.content_id=#{contentId} and co.parent_id=0 and co.del_flag = '0'
        order by co.comment_id DESC

    </select>
<!--    limit #{pageNum},#{pageSize}-->

    <select id="getOneLevelChildHaveAuthor"
            resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id, co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               u.nick_name as user_nick_name,u.avatar
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
        WHERE  co.comment_id in (
            SELECT MIN(comment_id)
            FROM campus_comment
            WHERE parent_id IN <foreach item="commentId"  collection="commentIds" open="(" separator="," close=")">
                                    #{commentId}
                                </foreach>
                AND user_id=#{userId} AND del_flag = '0'
            GROUP BY parent_id)

    </select>
    <select id="getOneLevelChild" parameterType="com.oddfar.campus.business.domain.entity.CommentEntity"
            resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id,co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               u.nick_name as user_nick_name,u.avatar,
               uu.nick_name as to_user_nick_name
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
                 LEFT JOIN sys_user uu on co.to_user_id=uu.user_id
        WHERE co.one_level_id=#{commentId} and co.parent_id!=0 and co.del_flag = '0'
    </select>
    <select id="selectOwnComment" resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id,co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               u.nick_name as user_nick_name,u.avatar,
               uu.nick_name as to_user_nick_name,
               (CASE WHEN LENGTH(c.content)>20
                         THEN CONCAT(SUBSTRING(c.content,1,10),'...')
                     ELSE c.content END) AS content_text
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
                 LEFT JOIN sys_user uu on co.to_user_id=uu.user_id
                 LEFT JOIN campus_content c on co.content_id=c.content_id
        WHERE  co.del_flag = '0' AND co.user_id=#{userId}
        order by co.create_time DESC
    </select>
    <select id="selectCommentVo" resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id,co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               u.nick_name as user_nick_name,u.avatar,
               uu.nick_name as to_user_nick_name
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
                 LEFT JOIN sys_user uu on co.to_user_id=uu.user_id
        WHERE co.comment_id=#{commentId}  and co.del_flag = '0'
    </select>
    <select id="getOneLevelChildList" resultType="com.oddfar.campus.business.domain.vo.CommentVo">
        SELECT co.comment_id,co.parent_id,co.user_id,co.to_user_id,co.one_level_id,co.content_id,co.co_content,co.ip,co.address,co.del_flag,co.create_time,
               u.nick_name as user_nick_name,u.avatar,
               uu.nick_name as to_user_nick_name
        FROM campus_comment co
                 LEFT JOIN sys_user u on co.user_id=u.user_id
                 LEFT JOIN sys_user uu on co.to_user_id=uu.user_id
        WHERE co.one_level_id=#{oneLevelId} and co.comment_id > #{commentId} and co.parent_id!=0 and co.del_flag = '0'
        limit 5
    </select>


</mapper>
