<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.framework.mapper.SysOperLogMapper">

    <resultMap type="SysOperLogEntity" id="SysOperLogResult">
        <id     property="operId"         column="oper_id"        />
        <result property="appName"          column="app_name"          />
        <result property="logName"          column="log_name"          />
        <result property="logContent"          column="log_content"          />
        <result property="method"         column="method"         />
        <result property="requestMethod"  column="request_method" />
        <result property="operUrl"        column="oper_url"       />
        <result property="operIp"         column="oper_ip"        />
        <result property="operParam"      column="oper_param"     />
        <result property="jsonResult"     column="json_result"    />
        <result property="status"         column="status"         />
        <result property="errorMsg"       column="error_msg"      />
        <result property="operTime"       column="oper_time"      />
    </resultMap>

    <update id="cleanOperLog">
        truncate table sys_log_oper
    </update>

</mapper>