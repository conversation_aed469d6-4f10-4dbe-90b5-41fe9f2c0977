<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.oddfar.campus.framework.mapper.SysRoleResourceMapper">


    <insert id="saveBatch">
        insert into sys_role_resource(role_id, resource_code) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.resourceCode})
        </foreach>
    </insert>
</mapper>