<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.business.mapper.ContentMapper">

    <resultMap id="BaseResultMap" type="com.oddfar.campus.business.domain.entity.ContentEntity">
        <id property="contentId" column="content_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="fileCount" column="file_count" jdbcType="TINYINT"/>
        <result property="loveCount" column="love_count" jdbcType="BIGINT"/>
        <result property="isAnonymous" column="is_anonymous" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
        <collection property="fileUrl" column="file_url" javaType="java.util.List"/>
    </resultMap>

    <resultMap id="ContentVoResult" type="ContentVo">
        <id property="contentId" column="content_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="fileCount" column="file_count" jdbcType="TINYINT"/>
        <result property="loveCount" column="love_count" jdbcType="BIGINT"/>
        <result property="isAnonymous" column="is_anonymous" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <association property="params" javaType="java.util.Map" >
            <result property="userId" column="user_id"/>
            <result property="userName" column="user_name"/>
            <result property="nickName" column="nick_name"/>
            <result property="avatar" column="avatar"/>
            <result property="categoryId" column="category_id"/>
            <result property="categoryName" column="category_name"/>
        </association>
        <!--        <association property="userEntity"  javaType="SysUserEntity">-->
        <!--            <id property="userId" column="user_id"/>-->
        <!--            <result property="userName" column="user_name"/>-->
        <!--            <result property="nickName" column="nick_name"/>-->
        <!--        </association>-->
        <!--        <association property="category"  javaType="CategoryEntity">-->
        <!--            <id property="categoryId" column="category_id"/>-->
        <!--            <result property="categoryName" column="category_name"/>-->
        <!--        </association>-->

        <collection property="tags" javaType="java.util.List" resultMap="tagResult"/>
    </resultMap>


    <resultMap id="tagResult" type="TagEntity">
        <id property="tagId" column="tag_id"/>
        <result property="tagName" column="tag_name"/>
    </resultMap>


    <sql id="selectContent">
        select DISTINCT c.content_id,
               c.user_id,
               c.category_id,
               c.content,
               c.status,
               c.type,
               c.file_count,
               c.love_count,
               c.is_anonymous,
               c.remark,
               c.del_flag,
               c.create_time,
               c.create_user,
               cc.category_name,
               u.user_name,
               u.nick_name,
               u.avatar

        from campus_content c
                 left join campus_category cc on cc.category_id = c.category_id
                 left join sys_user u on u.user_id = c.user_id

    </sql>

    <sql id="selectContentWhere">
        where c.del_flag = '0'
        <if test="params.userName != null and params.userName != ''">
            AND u.user_name like concat('%', #{params.userName}, '%')
        </if>
        <if test="params.nickName != null and params.nickName != ''">
            AND u.nick_name like concat('%', #{params.nickName}, '%')
        </if>
        <if test="params.categoryIds != null ">
            AND c.category_id in
            <foreach collection="params.categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">
            AND c.user_id = #{userId}
        </if>
        <if test="content != null and content != ''">
            AND c.content like concat('%', #{content}, '%')
        </if>
        <if test="status != null">
            AND c.status = #{status}
        </if>
        <if test="type != null">
            AND c.type = #{type}
        </if>
        <if test="isAnonymous != null">
            AND c.is_anonymous = #{isAnonymous}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(c.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(c.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>

    </sql>

    <sql id="selectContentOrderBy">
        <if test="params.orderBy != null and params.orderBy == 'hotContent'">
            order by c.love_count DESC
        </if>
        <if test="params.orderBy != null and params.orderBy == 'newest'">
            order by c.create_time DESC
        </if>
        <if test="params.orderBy == null">
            order by c.status asc,c.create_time DESC
        </if>
    </sql>

    <select id="selectContentList" parameterType="com.oddfar.campus.business.domain.entity.ContentEntity"
            resultMap="ContentVoResult">
        <include refid="selectContent"></include>
        <include refid="selectContentWhere"></include>
        <include refid="selectContentOrderBy"></include>
    </select>

    <select id="selectContentByContent" parameterType="com.oddfar.campus.business.domain.entity.ContentEntity"
            resultMap="ContentVoResult">
        <include refid="selectContent"></include>
        <include refid="selectContentWhere"></include>
        <if test="contentId != null and contentId != ''">
            AND c.content_id = #{contentId}
        </if>

    </select>
    <select id="selectLoveContentList" resultType="java.lang.Long">
        SELECT DISTINCT l.content_id
        FROM campus_content_love l
                 INNER JOIN campus_content c ON l.content_id = c.content_id AND c.user_id = #{userId} AND c.STATUS = 1 AND c.del_flag = '0'
        WHERE  l.user_id= #{userId}
    </select>

    <select id="selectContentByIds" resultMap="ContentVoResult">
        <include refid="selectContent"></include>
        WHERE c.content_id in   <foreach item="contentId"  collection="contentIdList" open="(" separator="," close=")">
                                 #{contentId}
                                </foreach>
        order by c.create_time DESC
    </select>

    <select id="getSimpleHotContent" resultType="com.oddfar.campus.business.domain.entity.ContentEntity">
        SELECT
            (
                CASE WHEN LENGTH(c.content)>20
                         THEN CONCAT(SUBSTRING(c.content,1,10),'...')
                     ELSE c.content END
                ) AS content, c.content_id, c.love_count
        FROM campus_content c
        WHERE c.del_flag = '0' and c.status = 1
        ORDER BY c.love_count DESC,c.create_time DESC
            LIMIT 0,10
    </select>

    <select id="getSimpleContentText" resultType="com.oddfar.campus.business.domain.entity.ContentEntity">
        SELECT
            (
                CASE WHEN LENGTH(c.content)>20
                         THEN CONCAT(SUBSTRING(c.content,1,10),'...')
                     ELSE c.content END
                ) AS content, c.content_id
        FROM campus_content c
        WHERE c.del_flag = '0' and c.status = 1
                and c.content_id in   <foreach item="contentId"  collection="contentIdList" open="(" separator="," close=")">
                                          #{contentId}
                                      </foreach>
        ORDER BY c.create_time DESC
    </select>

</mapper>
