<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.business.mapper.TagMapper">

    <resultMap id="BaseResultMap" type="com.oddfar.campus.business.domain.entity.TagEntity">
            <id property="tagId" column="tag_id" jdbcType="BIGINT"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="BIT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        tag_id,tag_name,description,
        del_flag,create_time,create_user,
        update_time,update_user
    </sql>

    <select id="selectTagListByContentIds" resultType="com.oddfar.campus.business.domain.entity.ContentTagEntity">
        select  ct.content_id ,t.tag_id ,t.tag_name
        FROM campus_content_tag  ct
                 INNER JOIN campus_tag t ON ct.tag_id = t.tag_id AND t.status=0 AND t.del_flag = '0'
        WHERE ct.content_id IN
        <foreach item="contentId"  collection="contentIds" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </select>
</mapper>
