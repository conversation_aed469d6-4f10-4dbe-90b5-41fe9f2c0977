<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.business.mapper.CategoryMapper">

    <resultMap id="BaseResultMap" type="com.oddfar.campus.business.domain.entity.CategoryEntity">
            <id property="categoryId" column="category_id" jdbcType="BIGINT"/>
            <result property="categoryName" column="name" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="slug" column="slug" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="BIT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        category_id,category_name,parent_id,
        slug,description,
        del_flag,create_time,create_user,
        update_time,update_user
    </sql>
</mapper>
