<p align="center"><a href="https://oddfar.com/" target="_blank" rel="noopener noreferrer"><img width="180" src="https://note.oddfar.com/img/web.png" alt="logo"></a></p>

<p align="center">
  <a href="https://github.com/oddfar/campus-example/stargazers"><img src="https://img.shields.io/github/stars/oddfar/campus-example.svg"></a>
	<a href="https://github.com/oddfar/campus/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>
<p align="center">Web 端使用 Vue + ElementUi，仿微博UI布局。 移动端使用 uni-app 和 uview，后端使用  SpringBoot + Mybatis-Plus进行开发</p>

<h2 align="center">Campus-exampus</h2>

 [项目文档](https://oddfar.github.io/campus-doc/campus-example)  | [笔记仓库](https://github.com/oddfar/notes)  |  [我的博客](https://oddfar.com)  



## 项目介绍

此项目使用 **Campus** 进行编写：<https://github.com/oddfar/campus>

> **Campus** 一款简单的后台管理系统，快速开发框架，适合大学生开发毕设，或其他小项目。

本项目为编写的一个例子——校园圈|信息墙|表白墙|万能墙

- 项目在线预览地址：暂无
- 项目文档：<https://oddfar.github.io/campus-doc/campus-example>

> 校园信息墙，万能墙，表白墙。是一个新式的信息交流的平台，有助于帮助学生及时查阅校内实时信息，在此平台去获取或者发布自己的供需信息。

Web 端使用 **Vue** + **ElementUi**，仿微博UI布局。 移动端使用 **uniapp** 和 **uview**，后端使用  **SpringBoot** + **Mybatis-Plus**进行开发

- PC网页端完全响应式布局

  支持电脑、平板、手机等所有主流设备

  部分样式暂时建议PC访问，还没有进行完善更改

- 移动端

  支持小程序、H5、Android和IOS

  精力有限，只调试了微信小程序，其它可能有样式问题，或兼容问题

  目前完成了绝大部分功能，可正常线上运行

## 项目代码

|                | GitHub                                          |
| -------------- | ----------------------------------------------- |
| 后端           | <https://github.com/oddfar/campus-example>      |
| nuxt web端     | <https://github.com/oddfar/nuxt_campus_example> |
| uni-app 移动端 | <https://github.com/oddfar/uni-app_campus_web>  |

vue后台代码在后端项目中

## 交流

欢迎各位老哥进群交流

<img src="https://note.oddfar.com/img/campus-example-group.jpg" alt="wechat" style="height:280px;" />

## 演示图



|                            admin                             |                                                              |
| :----------------------------------------------------------: | ------------------------------------------------------------ |
| ![image-20230221090307473](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221090307473.png) | ![image-20230221091209986](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221091209986.png) |
| ![image-20230221091408302](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221091408302.png) | ![image-20230221091331128](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221091331128.png) |



| web                                                          |                                                              |
| ------------------------------------------------------------ | ------------------------------------------------------------ |
| ![image-20230221091804015](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221091804015.png) | ![image-20230221091942563](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221091942563.png) |
| ![image-20230221092022247](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221092022247.png) | ![image-20230221092059047](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221092059047.png) |
| ![image-20230221092122331](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221092122331.png) | ![image-20230221092152935](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230221092152935.png) |



移动端（小程序）

| 移动端                                                       |                                                              |                                                              |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| ![image-20230412210057613](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210057613.png) | ![image-20230412210148423](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210148423.png) | ![image-20230412210214019](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210214019.png) |
| ![image-20230412210256253](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210256253.png) | ![image-20230412210328222](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210328222.png) | ![image-20230412210405497](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210405497.png) |
| ![image-20230412210534659](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210534659.png) | ![image-20230412210606552](https://gcore.jsdelivr.net/gh/oddfar/static/campus/doc/image-20230412210606552.png) |                                                              |



## 鸣谢

> [IntelliJ IDEA](https://zh.wikipedia.org/zh-hans/IntelliJ_IDEA) 是一个在各个方面都最大程度地提高开发人员的生产力的 IDE，适用于 JVM 平台语言。

特别感谢 [JetBrains](https://www.jetbrains.com/?from=campus) 为开源项目提供免费的 [IntelliJ IDEA](https://www.jetbrains.com/idea/?from=campus) 等 IDE 的授权  
[<img src=".github/jetbrains-variant.png" width="200"/>](https://www.jetbrains.com/?from=campus)
