<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oddfar.campus.framework.mapper.SysRoleMapper">

    <resultMap type="SysRoleEntity" id="SysRoleResult">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectRoleVo">
        select distinct r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.menu_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_time,
                        r.remark
        from sys_role r
                 left join sys_user_role ur on ur.role_id = r.role_id
                 left join sys_user u on u.user_id = ur.user_id
    </sql>



    <select id="selectRolePermissionByUserId" resultType="SysRoleEntity">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and ur.user_id = #{userId}
    </select>


    <select id="selectRoleById" parameterType="Long" resultType="SysRoleEntity">
        <include refid="selectRoleVo"/>
        where r.role_id = #{roleId}
    </select>


    <select id="selectRoleList" parameterType="SysRoleEntity" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.del_flag = '0'
        <if test="roleId != null and roleId != 0">
            AND r.role_id = #{roleId}
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name like concat('%', #{roleName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="roleKey != null and roleKey != ''">
            AND r.role_key like concat('%', #{roleKey}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        order by r.role_sort
    </select>

    <select id="selectRoleListByKey" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.del_flag = '0'
        and r.role_key in  <foreach item="roleKey"  collection="RoleKeys" open="(" separator="," close=")">
                              #{roleKey}
                           </foreach>
    </select>

    <select id="checkRoleNameUnique" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.role_name=#{roleName} and r.del_flag = '0' limit 1
    </select>

    <select id="checkRoleKeyUnique" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.role_key=#{roleKey} and r.del_flag = '0' limit 1
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and u.user_name = #{userName}
    </select>


</mapper>
